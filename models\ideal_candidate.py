from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, field_validator


class IdealCandidateBase(BaseModel):
    """Base model for ideal candidate data"""
    position_id: str
    proj_id: str
    ideal_candidate_info: dict
    generation_prompt: Optional[str] = None
    generation_model: Optional[str] = None
    is_active: bool = True


class IdealCandidateCreate(BaseModel):
    """Model for creating a new ideal candidate"""
    position_id: str
    proj_id: str
    ideal_candidate_info: dict
    generation_prompt: Optional[str] = None
    generation_model: Optional[str] = None
    created_by: str


class IdealCandidateUpdate(BaseModel):
    """Model for updating an existing ideal candidate"""
    id: str
    position_id: str
    proj_id: str
    ideal_candidate_info: dict
    generation_prompt: Optional[str] = None
    generation_model: Optional[str] = None
    updated_by: str


class IdealCandidate(IdealCandidateBase):
    """Complete ideal candidate model with database fields"""
    id: str
    to_be_embedded: Optional[str] = None  # Text prepared for embedding
    embedding_generated: bool = False
    created_at: datetime
    created_by: Optional[str] = None
    updated_at: datetime
    updated_by: Optional[str] = None

    class Config:
        from_attributes = True
        orm_mode = True
        validate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class IdealCandidateGenerationRequest(BaseModel):
    """Request model for generating ideal candidate from position"""
    position_id: str
    generation_options: Optional[dict] = None
    model_preference: Optional[str] = None


class IdealCandidateGenerationResponse(BaseModel):
    """Response model for ideal candidate generation"""
    ideal_candidate_id: str
    position_id: str
    ideal_candidate_info: dict
    generation_success: bool
    generation_time_ms: Optional[int] = None
    error_message: Optional[str] = None


class IdealCandidateFilters(BaseModel):
    """Filters for querying ideal candidates"""
    position_id: Optional[str] = None
    proj_id: Optional[str] = None
    is_active: Optional[bool] = None
    created_from: Optional[datetime] = None
    created_to: Optional[datetime] = None
    generation_model: Optional[str] = None

    @field_validator('created_from', 'created_to', mode='before')
    @classmethod
    def parse_empty_string_as_none(cls, v):
        if v == '':
            return None
        return v
