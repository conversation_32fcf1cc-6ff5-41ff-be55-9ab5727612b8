"""
Controller for ideal candidate database operations.
"""

import psycopg2
from psycopg2.extras import <PERSON><PERSON>
from contextlib import contextmanager
from typing import List, Optional, Dict, Any
import logging
from datetime import datetime

from config.config import settings
from models.ideal_candidate import (
    IdealCandidate,
    IdealCandidateCreate,
    IdealCandidateUpdate,
    IdealCandidateFilters
)
from utils.ideal_candidate_text_utils import prepare_ideal_candidate_for_embedding
from utils.utils_embeddings import format_vector, generate_openai_embedding

logger = logging.getLogger(__name__)


@contextmanager
def get_cursor():
    """Database cursor context manager."""
    conn = psycopg2.connect(settings.DATABASE_URL)
    try:
        with conn:
            with conn.cursor() as cur:
                yield cur
    finally:
        conn.close()


def create_ideal_candidate(ideal_candidate_data: IdealCandidateCreate) -> IdealCandidate:
    """
    Create a new ideal candidate in the database.
    
    Args:
        ideal_candidate_data: The ideal candidate data to create
        
    Returns:
        The created ideal candidate object
    """
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()
        
        # Prepare text for embedding
        embed_text = prepare_ideal_candidate_for_embedding(ideal_candidate_data.ideal_candidate_info)
        
        # Generate embedding
        embedding = generate_openai_embedding(embed_text)
        embedding_vector = format_vector(embedding) if embedding else None
        embedding_generated = embedding is not None
        
        logger.info(f"Creating ideal candidate for position {ideal_candidate_data.position_id}")
        
        cur.execute(
            """
            INSERT INTO ideal_candidates_smarthr 
            (position_id, proj_id, ideal_candidate_info, generation_prompt, generation_model, 
             to_be_embedded, embedding, embedding_generated, created_by, updated_by, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
            RETURNING id, position_id, proj_id, ideal_candidate_info, generation_prompt, generation_model,
                     to_be_embedded, embedding_generated, is_active, created_by, updated_by, created_at, updated_at
            """,
            (
                ideal_candidate_data.position_id,
                ideal_candidate_data.proj_id,
                Json(ideal_candidate_data.ideal_candidate_info),
                ideal_candidate_data.generation_prompt,
                ideal_candidate_data.generation_model,
                embed_text,
                embedding_vector,
                embedding_generated,
                ideal_candidate_data.created_by,
                ideal_candidate_data.created_by,
            ),
        )
        
        row = cur.fetchone()
        conn.commit()
        cur.close()
        conn.close()
        
        logger.info(f"Successfully created ideal candidate with ID: {row[0]}")
        
        return IdealCandidate(
            id=str(row[0]),
            position_id=str(row[1]),
            proj_id=str(row[2]),
            ideal_candidate_info=row[3],
            generation_prompt=row[4],
            generation_model=row[5],
            to_be_embedded=row[6],
            embedding_generated=row[7],
            is_active=row[8],
            created_by=row[9],
            updated_by=row[10],
            created_at=row[11],
            updated_at=row[12]
        )
        
    except Exception as e:
        logger.error(f"Error creating ideal candidate: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        raise


def get_ideal_candidate_by_id(ideal_candidate_id: str) -> Optional[IdealCandidate]:
    """
    Retrieve an ideal candidate by ID.
    
    Args:
        ideal_candidate_id: The ID of the ideal candidate to retrieve
        
    Returns:
        The ideal candidate object or None if not found
    """
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()
        
        cur.execute(
            """
            SELECT id, position_id, proj_id, ideal_candidate_info, generation_prompt, generation_model,
                   to_be_embedded, embedding_generated, is_active, created_by, updated_by, created_at, updated_at
            FROM ideal_candidates_smarthr
            WHERE id = %s AND is_active = true
            """,
            (ideal_candidate_id,)
        )
        
        row = cur.fetchone()
        cur.close()
        conn.close()
        
        if not row:
            return None
        
        return IdealCandidate(
            id=str(row[0]),
            position_id=str(row[1]),
            proj_id=str(row[2]),
            ideal_candidate_info=row[3],
            generation_prompt=row[4],
            generation_model=row[5],
            to_be_embedded=row[6],
            embedding_generated=row[7],
            is_active=row[8],
            created_by=row[9],
            updated_by=row[10],
            created_at=row[11],
            updated_at=row[12]
        )
        
    except Exception as e:
        logger.error(f"Error retrieving ideal candidate {ideal_candidate_id}: {e}")
        if 'conn' in locals():
            conn.close()
        return None


def get_ideal_candidate_by_position_id(position_id: str) -> Optional[IdealCandidate]:
    """
    Retrieve an ideal candidate by position ID.
    
    Args:
        position_id: The position ID to find ideal candidate for
        
    Returns:
        The ideal candidate object or None if not found
    """
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()
        
        cur.execute(
            """
            SELECT id, position_id, proj_id, ideal_candidate_info, generation_prompt, generation_model,
                   to_be_embedded, embedding_generated, is_active, created_by, updated_by, created_at, updated_at
            FROM ideal_candidates_smarthr
            WHERE position_id = %s AND is_active = true
            ORDER BY created_at DESC
            LIMIT 1
            """,
            (position_id,)
        )
        
        row = cur.fetchone()
        cur.close()
        conn.close()
        
        if not row:
            return None
        
        return IdealCandidate(
            id=str(row[0]),
            position_id=str(row[1]),
            proj_id=str(row[2]),
            ideal_candidate_info=row[3],
            generation_prompt=row[4],
            generation_model=row[5],
            to_be_embedded=row[6],
            embedding_generated=row[7],
            is_active=row[8],
            created_by=row[9],
            updated_by=row[10],
            created_at=row[11],
            updated_at=row[12]
        )
        
    except Exception as e:
        logger.error(f"Error retrieving ideal candidate for position {position_id}: {e}")
        if 'conn' in locals():
            conn.close()
        return None


def update_ideal_candidate(ideal_candidate_data: IdealCandidateUpdate) -> IdealCandidate:
    """
    Update an existing ideal candidate.
    
    Args:
        ideal_candidate_data: The updated ideal candidate data
        
    Returns:
        The updated ideal candidate object
    """
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()
        
        # Prepare text for embedding
        embed_text = prepare_ideal_candidate_for_embedding(ideal_candidate_data.ideal_candidate_info)
        
        # Check if embedding needs to be regenerated
        cur.execute(
            "SELECT to_be_embedded FROM ideal_candidates_smarthr WHERE id = %s",
            (ideal_candidate_data.id,)
        )
        current_embed_text = cur.fetchone()
        
        embedding_vector = None
        embedding_generated = False
        
        if not current_embed_text or current_embed_text[0] != embed_text:
            # Regenerate embedding if text changed
            embedding = generate_openai_embedding(embed_text)
            embedding_vector = format_vector(embedding) if embedding else None
            embedding_generated = embedding is not None
        
        logger.info(f"Updating ideal candidate {ideal_candidate_data.id}")
        
        if embedding_vector is not None:
            cur.execute(
                """
                UPDATE ideal_candidates_smarthr
                SET ideal_candidate_info = %s, generation_prompt = %s, generation_model = %s,
                    to_be_embedded = %s, embedding = %s, embedding_generated = %s,
                    updated_by = %s, updated_at = NOW()
                WHERE id = %s
                RETURNING id, position_id, proj_id, ideal_candidate_info, generation_prompt, generation_model,
                         to_be_embedded, embedding_generated, is_active, created_by, updated_by, created_at, updated_at
                """,
                (
                    Json(ideal_candidate_data.ideal_candidate_info),
                    ideal_candidate_data.generation_prompt,
                    ideal_candidate_data.generation_model,
                    embed_text,
                    embedding_vector,
                    embedding_generated,
                    ideal_candidate_data.updated_by,
                    ideal_candidate_data.id
                )
            )
        else:
            cur.execute(
                """
                UPDATE ideal_candidates_smarthr
                SET ideal_candidate_info = %s, generation_prompt = %s, generation_model = %s,
                    to_be_embedded = %s, updated_by = %s, updated_at = NOW()
                WHERE id = %s
                RETURNING id, position_id, proj_id, ideal_candidate_info, generation_prompt, generation_model,
                         to_be_embedded, embedding_generated, is_active, created_by, updated_by, created_at, updated_at
                """,
                (
                    Json(ideal_candidate_data.ideal_candidate_info),
                    ideal_candidate_data.generation_prompt,
                    ideal_candidate_data.generation_model,
                    embed_text,
                    ideal_candidate_data.updated_by,
                    ideal_candidate_data.id
                )
            )
        
        row = cur.fetchone()
        conn.commit()
        cur.close()
        conn.close()
        
        if not row:
            raise ValueError(f"Ideal candidate {ideal_candidate_data.id} not found")
        
        logger.info(f"Successfully updated ideal candidate {ideal_candidate_data.id}")
        
        return IdealCandidate(
            id=str(row[0]),
            position_id=str(row[1]),
            proj_id=str(row[2]),
            ideal_candidate_info=row[3],
            generation_prompt=row[4],
            generation_model=row[5],
            to_be_embedded=row[6],
            embedding_generated=row[7],
            is_active=row[8],
            created_by=row[9],
            updated_by=row[10],
            created_at=row[11],
            updated_at=row[12]
        )
        
    except Exception as e:
        logger.error(f"Error updating ideal candidate {ideal_candidate_data.id}: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        raise


def delete_ideal_candidate(ideal_candidate_id: str, deleted_by: str) -> bool:
    """
    Soft delete an ideal candidate by setting is_active to false.

    Args:
        ideal_candidate_id: The ID of the ideal candidate to delete
        deleted_by: The user performing the deletion

    Returns:
        True if deletion was successful, False otherwise
    """
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()

        cur.execute(
            """
            UPDATE ideal_candidates_smarthr
            SET is_active = false, updated_by = %s, updated_at = NOW()
            WHERE id = %s AND is_active = true
            """,
            (deleted_by, ideal_candidate_id)
        )

        rows_affected = cur.rowcount
        conn.commit()
        cur.close()
        conn.close()

        if rows_affected > 0:
            logger.info(f"Successfully deleted ideal candidate {ideal_candidate_id}")
            return True
        else:
            logger.warning(f"Ideal candidate {ideal_candidate_id} not found or already deleted")
            return False

    except Exception as e:
        logger.error(f"Error deleting ideal candidate {ideal_candidate_id}: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False


def get_ideal_candidates_by_filters(filters: IdealCandidateFilters, limit: int = 50, offset: int = 0) -> List[IdealCandidate]:
    """
    Retrieve ideal candidates based on filters.

    Args:
        filters: The filters to apply
        limit: Maximum number of results to return
        offset: Number of results to skip

    Returns:
        List of ideal candidate objects
    """
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()

        # Build query conditions
        conditions = ["is_active = true"]
        params = []

        if filters.position_id:
            conditions.append("position_id = %s")
            params.append(filters.position_id)

        if filters.proj_id:
            conditions.append("proj_id = %s")
            params.append(filters.proj_id)

        if filters.is_active is not None:
            conditions[-1] = "is_active = %s"  # Replace the default is_active condition
            params.insert(-1 if params else 0, filters.is_active)

        if filters.created_from:
            conditions.append("created_at >= %s")
            params.append(filters.created_from)

        if filters.created_to:
            conditions.append("created_at <= %s")
            params.append(filters.created_to)

        if filters.generation_model:
            conditions.append("generation_model = %s")
            params.append(filters.generation_model)

        where_clause = " AND ".join(conditions)
        params.extend([limit, offset])

        query = f"""
            SELECT id, position_id, proj_id, ideal_candidate_info, generation_prompt, generation_model,
                   to_be_embedded, embedding_generated, is_active, created_by, updated_by, created_at, updated_at
            FROM ideal_candidates_smarthr
            WHERE {where_clause}
            ORDER BY created_at DESC
            LIMIT %s OFFSET %s
        """

        cur.execute(query, params)
        rows = cur.fetchall()
        cur.close()
        conn.close()

        ideal_candidates = []
        for row in rows:
            ideal_candidates.append(IdealCandidate(
                id=str(row[0]),
                position_id=str(row[1]),
                proj_id=str(row[2]),
                ideal_candidate_info=row[3],
                generation_prompt=row[4],
                generation_model=row[5],
                to_be_embedded=row[6],
                embedding_generated=row[7],
                is_active=row[8],
                created_by=row[9],
                updated_by=row[10],
                created_at=row[11],
                updated_at=row[12]
            ))

        logger.info(f"Retrieved {len(ideal_candidates)} ideal candidates with filters")
        return ideal_candidates

    except Exception as e:
        logger.error(f"Error retrieving ideal candidates with filters: {e}")
        if 'conn' in locals():
            conn.close()
        return []


def get_ideal_candidate_embedding_text(ideal_candidate_id: str) -> Optional[str]:
    """
    Get the embedding text for an ideal candidate by ID.

    Args:
        ideal_candidate_id: The ID of the ideal candidate

    Returns:
        The embedding text or None if not found
    """
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()

        cur.execute(
            "SELECT to_be_embedded FROM ideal_candidates_smarthr WHERE id = %s",
            (ideal_candidate_id,)
        )

        row = cur.fetchone()
        cur.close()
        conn.close()

        return row[0] if row else None

    except Exception as e:
        logger.error(f"Error retrieving embedding text for ideal candidate {ideal_candidate_id}: {e}")
        if 'conn' in locals():
            conn.close()
        return None


def search_similar_ideal_candidates(embedding_vector: List[float], limit: int = 5, proj_id: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Search for similar ideal candidates using vector similarity.

    Args:
        embedding_vector: The embedding vector to search with
        limit: Maximum number of results to return
        proj_id: Optional project ID to filter by

    Returns:
        List of dictionaries containing ideal candidate data and similarity scores
    """
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()

        # Format vector for PostgreSQL
        embedding_vector_formatted = format_vector(embedding_vector)

        if proj_id:
            query = """
                SELECT id, position_id, proj_id, ideal_candidate_info, to_be_embedded,
                       1 - (embedding <=> %s) AS cosine_similarity
                FROM ideal_candidates_smarthr
                WHERE is_active = true AND embedding_generated = true AND proj_id = %s
                ORDER BY cosine_similarity DESC
                LIMIT %s
            """
            params = (embedding_vector_formatted, proj_id, limit)
        else:
            query = """
                SELECT id, position_id, proj_id, ideal_candidate_info, to_be_embedded,
                       1 - (embedding <=> %s) AS cosine_similarity
                FROM ideal_candidates_smarthr
                WHERE is_active = true AND embedding_generated = true
                ORDER BY cosine_similarity DESC
                LIMIT %s
            """
            params = (embedding_vector_formatted, limit)

        cur.execute(query, params)
        results = cur.fetchall()
        cur.close()
        conn.close()

        similar_candidates = []
        for row in results:
            similar_candidates.append({
                "id": str(row[0]),
                "position_id": str(row[1]),
                "proj_id": str(row[2]),
                "ideal_candidate_info": row[3],
                "to_be_embedded": row[4],
                "cosine_similarity": float(row[5])
            })

        logger.info(f"Found {len(similar_candidates)} similar ideal candidates")
        return similar_candidates

    except Exception as e:
        logger.error(f"Error searching similar ideal candidates: {e}")
        if 'conn' in locals():
            conn.close()
        return []
