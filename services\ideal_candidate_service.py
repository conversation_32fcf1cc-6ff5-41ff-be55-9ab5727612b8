"""
Service for generating ideal candidate profiles from position data using LLM.
"""

import time
from typing import Optional, Dict, Any
from datetime import datetime
import logging

from models.ideal_candidate import (
    IdealCandidate,
    IdealCandidateCreate,
    IdealCandidateGenerationRequest,
    IdealCandidateGenerationResponse
)
from models.llm import inference_with_fallback
from langchain_core.messages import HumanMessage
from config.config import MODELS_CONFIG

logger = logging.getLogger(__name__)


class IdealCandidateGenerator:
    """Service class for generating ideal candidate profiles from positions."""
    
    def __init__(self):
        self.default_models_order = MODELS_CONFIG.get("ideal_candidate_models_order", ["gpt-4o", "gpt-4o-mini"])
    
    def generate_ideal_candidate_prompt(self) -> str:
        """
        Generate the system prompt for creating ideal candidate profiles.
        """
        return """
        You are an expert talent acquisition specialist and recruiter with deep expertise in creating ideal candidate profiles.
        
        Your task is to analyze a position description and generate a comprehensive ideal candidate profile that would be the perfect match for this position.
        
        ## INSTRUCTIONS:
        
        1. **Analyze the Position**: Carefully review the position requirements, responsibilities, skills, and context.
        
        2. **Generate Ideal Candidate Profile**: Create a detailed candidate profile that includes:
           - Professional summary that aligns perfectly with the position
           - Relevant work experience and career progression
           - Technical skills that match or exceed requirements
           - Soft skills and competencies needed for success
           - Educational background and certifications
           - Industry experience and domain knowledge
           - Leadership and project management experience (if applicable)
           - Cultural fit indicators
        
        3. **Be Specific and Realistic**: 
           - Use concrete examples and specific technologies/tools
           - Ensure the profile is achievable and realistic
           - Include both required and nice-to-have qualifications
           - Consider career progression and growth trajectory
        
        4. **Structure the Response**: Provide a well-organized JSON response with the following structure:
        
        ```json
        {
            "personal_info": {
                "professional_title": "Current role title",
                "years_of_experience": "X-Y years",
                "location_preference": "Location details",
                "summary": "Professional summary paragraph"
            },
            "technical_skills": {
                "core_technologies": ["skill1", "skill2", "skill3"],
                "frameworks_tools": ["framework1", "tool1", "tool2"],
                "programming_languages": ["lang1", "lang2"],
                "databases": ["db1", "db2"],
                "cloud_platforms": ["platform1", "platform2"],
                "other_technical": ["skill1", "skill2"]
            },
            "professional_experience": [
                {
                    "role": "Job Title",
                    "company_type": "Type of company",
                    "duration": "X years",
                    "key_achievements": ["achievement1", "achievement2"],
                    "technologies_used": ["tech1", "tech2"]
                }
            ],
            "education": {
                "degree": "Degree type and field",
                "additional_certifications": ["cert1", "cert2"],
                "continuous_learning": ["course1", "course2"]
            },
            "soft_skills": [
                "Communication",
                "Leadership",
                "Problem-solving"
            ],
            "industry_experience": {
                "domains": ["domain1", "domain2"],
                "company_sizes": ["startup", "enterprise"],
                "project_types": ["type1", "type2"]
            },
            "leadership_management": {
                "team_leadership": "Experience level",
                "project_management": "Experience level",
                "mentoring": "Experience level"
            },
            "cultural_fit": {
                "work_style": "Description",
                "values_alignment": "Description",
                "collaboration_style": "Description"
            },
            "additional_qualifications": {
                "languages": ["language1", "language2"],
                "publications": ["publication1"],
                "speaking_conferences": ["conference1"],
                "open_source_contributions": ["project1"]
            }
        }
        ```
        
        ## IMPORTANT GUIDELINES:
        - Base the ideal candidate strictly on the position requirements
        - Be specific with technologies, tools, and frameworks mentioned in the position
        - Ensure the experience level matches the seniority required
        - Include both technical and soft skills relevant to the role
        - Consider the company culture and work environment if mentioned
        - Make the profile comprehensive but realistic
        - Avoid generic statements; be specific and detailed
        
        Return only valid JSON. No additional commentary outside the JSON structure.
        """
    
    def generate_ideal_candidate_from_position(
        self, 
        position_text: str, 
        position_info: Dict[str, Any],
        generation_options: Optional[Dict[str, Any]] = None,
        model_preference: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Generate an ideal candidate profile from position data.
        
        Args:
            position_text: The text representation of the position for embedding
            position_info: The structured position information
            generation_options: Optional generation parameters
            model_preference: Preferred model for generation
            
        Returns:
            Dictionary containing the ideal candidate profile
        """
        start_time = time.time()
        
        try:
            # Prepare the system prompt
            system_prompt = self.generate_ideal_candidate_prompt()
            
            # Prepare user messages with position information
            user_messages = [
                HumanMessage(content=f"Position Text for Embedding: {position_text}"),
                HumanMessage(content=f"Structured Position Information: {position_info}")
            ]
            
            # Add generation options if provided
            if generation_options:
                user_messages.append(
                    HumanMessage(content=f"Generation Options: {generation_options}")
                )
            
            # Determine models order
            models_order = self.default_models_order
            if model_preference and model_preference in models_order:
                # Move preferred model to front
                models_order = [model_preference] + [m for m in models_order if m != model_preference]
            
            logger.info(f"Generating ideal candidate with models order: {models_order}")
            
            # Generate ideal candidate using LLM
            result = inference_with_fallback(
                task_prompt=system_prompt,
                model_schema=None,  # We'll parse JSON manually for flexibility
                user_messages=user_messages,
                model_schema_text=None,
                models_order=models_order
            )
            
            if not result:
                raise RuntimeError("All LLM providers failed to generate ideal candidate")
            
            # Extract content from result
            if hasattr(result, 'content'):
                content = result.content
            else:
                content = str(result)
            
            # Parse JSON response
            import json
            try:
                ideal_candidate_data = json.loads(content)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM response as JSON: {e}")
                logger.error(f"Raw response: {content}")
                raise ValueError(f"Invalid JSON response from LLM: {e}")
            
            generation_time_ms = int((time.time() - start_time) * 1000)
            
            logger.info(f"Successfully generated ideal candidate in {generation_time_ms}ms")
            
            return {
                "ideal_candidate_info": ideal_candidate_data,
                "generation_time_ms": generation_time_ms,
                "generation_model": models_order[0] if models_order else "unknown",
                "generation_success": True
            }
            
        except Exception as e:
            generation_time_ms = int((time.time() - start_time) * 1000)
            logger.error(f"Failed to generate ideal candidate: {e}")
            
            return {
                "ideal_candidate_info": {},
                "generation_time_ms": generation_time_ms,
                "generation_model": model_preference or "unknown",
                "generation_success": False,
                "error_message": str(e)
            }


# Global instance
ideal_candidate_generator = IdealCandidateGenerator()
