"""
Complete workflow service for ideal candidate generation and management.
"""

import logging
from typing import Optional, Dict, Any
from datetime import datetime

from models.ideal_candidate import (
    IdealCandidateCreate,
    IdealCandidateGenerationRequest,
    IdealCandidateGenerationResponse
)
from services.ideal_candidate_service import ideal_candidate_generator
from controllers.ideal_candidate_controller import (
    create_ideal_candidate,
    get_ideal_candidate_by_position_id,
    update_ideal_candidate
)
from controllers.positions_controller import get_position_by_id
from templates.positions_templates.positions.json_column import prepare_position_for_embedding

logger = logging.getLogger(__name__)


class IdealCandidateWorkflow:
    """Service class for managing the complete ideal candidate workflow."""
    
    def __init__(self):
        self.generator = ideal_candidate_generator
    
    def generate_ideal_candidate_for_position(
        self, 
        request: IdealCandidateGenerationRequest,
        created_by: str = "system"
    ) -> IdealCandidateGenerationResponse:
        """
        Generate an ideal candidate for a given position.
        
        Args:
            request: The generation request containing position_id and options
            created_by: The user creating the ideal candidate
            
        Returns:
            Response containing the generated ideal candidate or error information
        """
        start_time = datetime.now()
        
        try:
            logger.info(f"Starting ideal candidate generation for position {request.position_id}")
            
            # 1. Retrieve the position data
            position = get_position_by_id(request.position_id)
            if not position:
                return IdealCandidateGenerationResponse(
                    ideal_candidate_id="",
                    position_id=request.position_id,
                    ideal_candidate_info={},
                    generation_success=False,
                    error_message=f"Position {request.position_id} not found"
                )
            
            # 2. Check if ideal candidate already exists for this position
            existing_ideal_candidate = get_ideal_candidate_by_position_id(request.position_id)
            if existing_ideal_candidate:
                logger.info(f"Found existing ideal candidate {existing_ideal_candidate.id} for position {request.position_id}")
                
                generation_time_ms = int((datetime.now() - start_time).total_seconds() * 1000)
                
                return IdealCandidateGenerationResponse(
                    ideal_candidate_id=existing_ideal_candidate.id,
                    position_id=request.position_id,
                    ideal_candidate_info=existing_ideal_candidate.ideal_candidate_info,
                    generation_success=True,
                    generation_time_ms=generation_time_ms
                )
            
            # 3. Prepare position text for embedding
            position_text = prepare_position_for_embedding(position.proj_id, position.position_info)
            
            # 4. Generate ideal candidate using LLM
            generation_result = self.generator.generate_ideal_candidate_from_position(
                position_text=position_text,
                position_info=position.position_info,
                generation_options=request.generation_options,
                model_preference=request.model_preference
            )
            
            if not generation_result["generation_success"]:
                return IdealCandidateGenerationResponse(
                    ideal_candidate_id="",
                    position_id=request.position_id,
                    ideal_candidate_info={},
                    generation_success=False,
                    generation_time_ms=generation_result.get("generation_time_ms", 0),
                    error_message=generation_result.get("error_message", "Unknown generation error")
                )
            
            # 5. Create ideal candidate in database
            ideal_candidate_create = IdealCandidateCreate(
                position_id=request.position_id,
                proj_id=position.proj_id,
                ideal_candidate_info=generation_result["ideal_candidate_info"],
                generation_prompt=self.generator.generate_ideal_candidate_prompt(),
                generation_model=generation_result.get("generation_model"),
                created_by=created_by
            )
            
            ideal_candidate = create_ideal_candidate(ideal_candidate_create)
            
            generation_time_ms = int((datetime.now() - start_time).total_seconds() * 1000)
            
            logger.info(f"Successfully generated ideal candidate {ideal_candidate.id} for position {request.position_id}")
            
            return IdealCandidateGenerationResponse(
                ideal_candidate_id=ideal_candidate.id,
                position_id=request.position_id,
                ideal_candidate_info=ideal_candidate.ideal_candidate_info,
                generation_success=True,
                generation_time_ms=generation_time_ms
            )
            
        except Exception as e:
            generation_time_ms = int((datetime.now() - start_time).total_seconds() * 1000)
            logger.error(f"Error in ideal candidate generation workflow: {e}")
            
            return IdealCandidateGenerationResponse(
                ideal_candidate_id="",
                position_id=request.position_id,
                ideal_candidate_info={},
                generation_success=False,
                generation_time_ms=generation_time_ms,
                error_message=str(e)
            )
    
    def regenerate_ideal_candidate_for_position(
        self,
        position_id: str,
        generation_options: Optional[Dict[str, Any]] = None,
        model_preference: Optional[str] = None,
        updated_by: str = "system"
    ) -> IdealCandidateGenerationResponse:
        """
        Regenerate an ideal candidate for a position (force regeneration even if one exists).
        
        Args:
            position_id: The position ID to regenerate ideal candidate for
            generation_options: Optional generation parameters
            model_preference: Preferred model for generation
            updated_by: The user updating the ideal candidate
            
        Returns:
            Response containing the regenerated ideal candidate or error information
        """
        start_time = datetime.now()
        
        try:
            logger.info(f"Regenerating ideal candidate for position {position_id}")
            
            # 1. Retrieve the position data
            position = get_position_by_id(position_id)
            if not position:
                return IdealCandidateGenerationResponse(
                    ideal_candidate_id="",
                    position_id=position_id,
                    ideal_candidate_info={},
                    generation_success=False,
                    error_message=f"Position {position_id} not found"
                )
            
            # 2. Prepare position text for embedding
            position_text = prepare_position_for_embedding(position.proj_id, position.position_info)
            
            # 3. Generate new ideal candidate using LLM
            generation_result = self.generator.generate_ideal_candidate_from_position(
                position_text=position_text,
                position_info=position.position_info,
                generation_options=generation_options,
                model_preference=model_preference
            )
            
            if not generation_result["generation_success"]:
                return IdealCandidateGenerationResponse(
                    ideal_candidate_id="",
                    position_id=position_id,
                    ideal_candidate_info={},
                    generation_success=False,
                    generation_time_ms=generation_result.get("generation_time_ms", 0),
                    error_message=generation_result.get("error_message", "Unknown generation error")
                )
            
            # 4. Check if ideal candidate exists and update or create
            existing_ideal_candidate = get_ideal_candidate_by_position_id(position_id)
            
            if existing_ideal_candidate:
                # Update existing ideal candidate
                from models.ideal_candidate import IdealCandidateUpdate
                
                ideal_candidate_update = IdealCandidateUpdate(
                    id=existing_ideal_candidate.id,
                    position_id=position_id,
                    proj_id=position.proj_id,
                    ideal_candidate_info=generation_result["ideal_candidate_info"],
                    generation_prompt=self.generator.generate_ideal_candidate_prompt(),
                    generation_model=generation_result.get("generation_model"),
                    updated_by=updated_by
                )
                
                ideal_candidate = update_ideal_candidate(ideal_candidate_update)
                logger.info(f"Updated existing ideal candidate {ideal_candidate.id}")
            else:
                # Create new ideal candidate
                ideal_candidate_create = IdealCandidateCreate(
                    position_id=position_id,
                    proj_id=position.proj_id,
                    ideal_candidate_info=generation_result["ideal_candidate_info"],
                    generation_prompt=self.generator.generate_ideal_candidate_prompt(),
                    generation_model=generation_result.get("generation_model"),
                    created_by=updated_by
                )
                
                ideal_candidate = create_ideal_candidate(ideal_candidate_create)
                logger.info(f"Created new ideal candidate {ideal_candidate.id}")
            
            generation_time_ms = int((datetime.now() - start_time).total_seconds() * 1000)
            
            return IdealCandidateGenerationResponse(
                ideal_candidate_id=ideal_candidate.id,
                position_id=position_id,
                ideal_candidate_info=ideal_candidate.ideal_candidate_info,
                generation_success=True,
                generation_time_ms=generation_time_ms
            )
            
        except Exception as e:
            generation_time_ms = int((datetime.now() - start_time).total_seconds() * 1000)
            logger.error(f"Error in ideal candidate regeneration workflow: {e}")
            
            return IdealCandidateGenerationResponse(
                ideal_candidate_id="",
                position_id=position_id,
                ideal_candidate_info={},
                generation_success=False,
                generation_time_ms=generation_time_ms,
                error_message=str(e)
            )
    
    def get_or_generate_ideal_candidate(
        self,
        position_id: str,
        force_regenerate: bool = False,
        generation_options: Optional[Dict[str, Any]] = None,
        model_preference: Optional[str] = None,
        user: str = "system"
    ) -> IdealCandidateGenerationResponse:
        """
        Get existing ideal candidate or generate a new one if it doesn't exist.
        
        Args:
            position_id: The position ID
            force_regenerate: Whether to force regeneration even if one exists
            generation_options: Optional generation parameters
            model_preference: Preferred model for generation
            user: The user performing the operation
            
        Returns:
            Response containing the ideal candidate or error information
        """
        if force_regenerate:
            return self.regenerate_ideal_candidate_for_position(
                position_id=position_id,
                generation_options=generation_options,
                model_preference=model_preference,
                updated_by=user
            )
        else:
            request = IdealCandidateGenerationRequest(
                position_id=position_id,
                generation_options=generation_options,
                model_preference=model_preference
            )
            return self.generate_ideal_candidate_for_position(request, created_by=user)


# Global instance
ideal_candidate_workflow = IdealCandidateWorkflow()
