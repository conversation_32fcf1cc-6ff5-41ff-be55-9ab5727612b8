"""
Utility functions for database queries related to ideal candidates.
"""

import logging
import psycopg2
from typing import List, Dict, Any, Optional, Tuple

from config.config import settings

logger = logging.getLogger(__name__)


def get_candidate_query_with_ideal_candidate(hasFeedback: int) -> str:
    """
    Get the candidate query that uses ideal candidate embeddings instead of position embeddings.
    
    Args:
        hasFeedback: Feedback filter (0=without, 1=with, 2=both)
        
    Returns:
        SQL query string for candidate matching with ideal candidates
    """
    if hasFeedback == 0:
        # Without feedback - candidates that haven't been interviewed for this position
        return """
            SELECT 
                c.id, 
                c.proj_id, 
                c.candidate_info, 
                1 - (c.embedding <=> %s) AS cosine_similarity, 
                c.to_be_embebbed,
                COUNT(i.id) AS num_feedbacks
            FROM 
                candidates_smarthr c
            LEFT JOIN 
                interviews i ON i.candidate_id = c.id AND i.position_id = %s
            WHERE 
                c.is_deleted = false 
                AND c.is_active = true
            GROUP BY 
                c.id, c.proj_id, c.candidate_info, c.to_be_embebbed, c.embedding
            HAVING 
                COUNT(i.id) = 0
            ORDER BY 
                cosine_similarity DESC
            LIMIT %s
        """
    elif hasFeedback == 1:
        # With feedback - candidates that have been interviewed for this position
        return """
            SELECT 
                c.id, 
                c.proj_id, 
                c.candidate_info, 
                1 - (c.embedding <=> %s) AS cosine_similarity, 
                c.to_be_embebbed,
                COUNT(i.id) AS num_feedbacks
            FROM 
                candidates_smarthr c
            INNER JOIN 
                interviews i ON i.candidate_id = c.id AND i.position_id = %s
            WHERE 
                c.is_deleted = false 
                AND c.is_active = true
            GROUP BY 
                c.id, c.proj_id, c.candidate_info, c.to_be_embebbed, c.embedding
            HAVING 
                COUNT(i.id) > 0
            ORDER BY 
                cosine_similarity DESC
            LIMIT %s
        """
    else:
        # Both (hasFeedback == 2) - all candidates regardless of feedback
        return """
            SELECT 
                c.id, 
                c.proj_id, 
                c.candidate_info, 
                1 - (c.embedding <=> %s) AS cosine_similarity, 
                c.to_be_embebbed
            FROM 
                candidates_smarthr c
            WHERE 
                c.is_deleted = false 
                AND c.is_active = true
            ORDER BY 
                cosine_similarity DESC
            LIMIT %s
        """


def get_position_query_with_ideal_candidate() -> str:
    """
    Get the position query that uses ideal candidate embeddings for matching.
    
    Returns:
        SQL query string for position matching with ideal candidates
    """
    return """
        SELECT 
            p.id, 
            p.proj_id, 
            p.position_info, 
            1 - (ic.embedding <=> %s) AS cosine_similarity, 
            ic.to_be_embedded,
            ic.id as ideal_candidate_id,
            ic.ideal_candidate_info
        FROM 
            positions_smarthr p
        INNER JOIN 
            ideal_candidates_smarthr ic ON ic.position_id = p.id
        WHERE 
            ic.is_active = true 
            AND ic.embedding_generated = true
        ORDER BY 
            cosine_similarity DESC
        LIMIT %s
    """


def get_ideal_candidate_embedding_for_position(position_id: str) -> Optional[List[float]]:
    """
    Get the embedding vector for the ideal candidate of a given position.
    
    Args:
        position_id: The position ID to get ideal candidate embedding for
        
    Returns:
        The embedding vector or None if not found
    """
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()
        
        cur.execute(
            """
            SELECT embedding 
            FROM ideal_candidates_smarthr 
            WHERE position_id = %s AND is_active = true AND embedding_generated = true
            ORDER BY created_at DESC
            LIMIT 1
            """,
            (position_id,)
        )
        
        row = cur.fetchone()
        cur.close()
        conn.close()
        
        if row and row[0]:
            return row[0]  # PostgreSQL returns the vector as a list
        else:
            logger.warning(f"No embedding found for ideal candidate of position {position_id}")
            return None
            
    except Exception as e:
        logger.error(f"Error getting ideal candidate embedding for position {position_id}: {e}")
        if 'conn' in locals():
            conn.close()
        return None


def check_ideal_candidate_availability(position_id: str) -> Dict[str, Any]:
    """
    Check if an ideal candidate exists and has embedding for a given position.
    
    Args:
        position_id: The position ID to check
        
    Returns:
        Dictionary with availability information
    """
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()
        
        cur.execute(
            """
            SELECT id, embedding_generated, created_at, updated_at
            FROM ideal_candidates_smarthr 
            WHERE position_id = %s AND is_active = true
            ORDER BY created_at DESC
            LIMIT 1
            """,
            (position_id,)
        )
        
        row = cur.fetchone()
        cur.close()
        conn.close()
        
        if row:
            return {
                "exists": True,
                "ideal_candidate_id": str(row[0]),
                "embedding_generated": row[1],
                "created_at": row[2],
                "updated_at": row[3],
                "ready_for_matching": row[1]  # True if embedding is generated
            }
        else:
            return {
                "exists": False,
                "ideal_candidate_id": None,
                "embedding_generated": False,
                "created_at": None,
                "updated_at": None,
                "ready_for_matching": False
            }
            
    except Exception as e:
        logger.error(f"Error checking ideal candidate availability for position {position_id}: {e}")
        if 'conn' in locals():
            conn.close()
        return {
            "exists": False,
            "ideal_candidate_id": None,
            "embedding_generated": False,
            "created_at": None,
            "updated_at": None,
            "ready_for_matching": False,
            "error": str(e)
        }


def get_positions_with_ideal_candidates(proj_id: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
    """
    Get all positions that have ideal candidates with embeddings.
    
    Args:
        proj_id: Optional project ID to filter by
        limit: Maximum number of positions to return
        
    Returns:
        List of positions with their ideal candidate information
    """
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()
        
        if proj_id:
            query = """
                SELECT 
                    p.id as position_id,
                    p.proj_id,
                    p.position_info,
                    ic.id as ideal_candidate_id,
                    ic.ideal_candidate_info,
                    ic.embedding_generated,
                    ic.created_at as ideal_candidate_created_at
                FROM 
                    positions_smarthr p
                INNER JOIN 
                    ideal_candidates_smarthr ic ON ic.position_id = p.id
                WHERE 
                    p.proj_id = %s
                    AND ic.is_active = true 
                    AND ic.embedding_generated = true
                ORDER BY 
                    ic.created_at DESC
                LIMIT %s
            """
            params = (proj_id, limit)
        else:
            query = """
                SELECT 
                    p.id as position_id,
                    p.proj_id,
                    p.position_info,
                    ic.id as ideal_candidate_id,
                    ic.ideal_candidate_info,
                    ic.embedding_generated,
                    ic.created_at as ideal_candidate_created_at
                FROM 
                    positions_smarthr p
                INNER JOIN 
                    ideal_candidates_smarthr ic ON ic.position_id = p.id
                WHERE 
                    ic.is_active = true 
                    AND ic.embedding_generated = true
                ORDER BY 
                    ic.created_at DESC
                LIMIT %s
            """
            params = (limit,)
        
        cur.execute(query, params)
        rows = cur.fetchall()
        cur.close()
        conn.close()
        
        positions = []
        for row in rows:
            positions.append({
                "position_id": str(row[0]),
                "proj_id": str(row[1]),
                "position_info": row[2],
                "ideal_candidate_id": str(row[3]),
                "ideal_candidate_info": row[4],
                "embedding_generated": row[5],
                "ideal_candidate_created_at": row[6]
            })
        
        logger.info(f"Found {len(positions)} positions with ideal candidates")
        return positions
        
    except Exception as e:
        logger.error(f"Error getting positions with ideal candidates: {e}")
        if 'conn' in locals():
            conn.close()
        return []


def get_matching_statistics(proj_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Get statistics about ideal candidate matching readiness.
    
    Args:
        proj_id: Optional project ID to filter by
        
    Returns:
        Dictionary containing matching statistics
    """
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()
        
        # Base conditions
        where_conditions = []
        params = []
        
        if proj_id:
            where_conditions.append("p.proj_id = %s")
            params.append(proj_id)
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        # Total positions
        cur.execute(f"SELECT COUNT(*) FROM positions_smarthr p WHERE {where_clause}", params)
        total_positions = cur.fetchone()[0]
        
        # Positions with ideal candidates
        cur.execute(f"""
            SELECT COUNT(DISTINCT p.id) 
            FROM positions_smarthr p
            INNER JOIN ideal_candidates_smarthr ic ON ic.position_id = p.id
            WHERE {where_clause} AND ic.is_active = true
        """, params)
        positions_with_ideal_candidates = cur.fetchone()[0]
        
        # Positions ready for matching (with embeddings)
        cur.execute(f"""
            SELECT COUNT(DISTINCT p.id) 
            FROM positions_smarthr p
            INNER JOIN ideal_candidates_smarthr ic ON ic.position_id = p.id
            WHERE {where_clause} AND ic.is_active = true AND ic.embedding_generated = true
        """, params)
        positions_ready_for_matching = cur.fetchone()[0]
        
        cur.close()
        conn.close()
        
        statistics = {
            "total_positions": total_positions,
            "positions_with_ideal_candidates": positions_with_ideal_candidates,
            "positions_ready_for_matching": positions_ready_for_matching,
            "positions_without_ideal_candidates": total_positions - positions_with_ideal_candidates,
            "ideal_candidate_coverage_percentage": (positions_with_ideal_candidates / total_positions * 100) if total_positions > 0 else 0,
            "matching_readiness_percentage": (positions_ready_for_matching / total_positions * 100) if total_positions > 0 else 0,
            "proj_id": proj_id
        }
        
        logger.info(f"Matching statistics: {statistics}")
        return statistics
        
    except Exception as e:
        logger.error(f"Error getting matching statistics: {e}")
        if 'conn' in locals():
            conn.close()
        return {
            "total_positions": 0,
            "positions_with_ideal_candidates": 0,
            "positions_ready_for_matching": 0,
            "positions_without_ideal_candidates": 0,
            "ideal_candidate_coverage_percentage": 0,
            "matching_readiness_percentage": 0,
            "proj_id": proj_id,
            "error": str(e)
        }
